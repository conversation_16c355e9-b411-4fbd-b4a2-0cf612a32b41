-- JJWX 信息协同处理平台 - 数据库初始化脚本
-- {{CHENGQI:
-- Action: Added
-- Timestamp: 2025-06-04 16:25:00 +08:00
-- Task_ID: P1-LD-002
-- Principle_Applied: KISS - 简洁的数据库结构，只包含必要的表和字段
-- Language: SQL
-- Description: 创建完整的数据库表结构，包含用户权限、信息管理、关联管理、审计日志等核心功能
-- }}

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- ========================================
-- 1. 用户权限管理表
-- ========================================

-- 1.1 部门表
CREATE TABLE IF NOT EXISTS department (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    parent_id INTEGER,
    level INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES department(id)
);

CREATE INDEX IF NOT EXISTS idx_department_parent ON department(parent_id);
CREATE INDEX IF NOT EXISTS idx_department_active ON department(is_active);
CREATE INDEX IF NOT EXISTS idx_department_sort ON department(sort_order);

-- 1.2 用户表
CREATE TABLE IF NOT EXISTS user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    department_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES department(id)
);

CREATE INDEX IF NOT EXISTS idx_user_username ON user(username);
CREATE INDEX IF NOT EXISTS idx_user_department ON user(department_id);
CREATE INDEX IF NOT EXISTS idx_user_status ON user(status);

-- 1.3 角色表
CREATE TABLE IF NOT EXISTS role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_role_active ON role(is_active);

-- 1.4 权限表
CREATE TABLE IF NOT EXISTS permission (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_permission_resource ON permission(resource);
CREATE INDEX IF NOT EXISTS idx_permission_action ON permission(action);
CREATE INDEX IF NOT EXISTS idx_permission_active ON permission(is_active);

-- 1.5 用户角色关联表
CREATE TABLE IF NOT EXISTS user_role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
    UNIQUE(user_id, role_id)
);

CREATE INDEX IF NOT EXISTS idx_user_role_user ON user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_user_role_role ON user_role(role_id);

-- 1.6 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permission (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    role_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permission(id) ON DELETE CASCADE,
    UNIQUE(role_id, permission_id)
);

CREATE INDEX IF NOT EXISTS idx_role_permission_role ON role_permission(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permission_permission ON role_permission(permission_id);

-- ========================================
-- 2. 信息管理表
-- ========================================

-- 2.1 信息来源渠道表
CREATE TABLE IF NOT EXISTS source_channel (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_source_channel_active ON source_channel(is_active);

-- 2.2 上级规定分类表
CREATE TABLE IF NOT EXISTS official_category (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    parent_id INTEGER,
    level INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES official_category(id)
);

CREATE INDEX IF NOT EXISTS idx_official_category_parent ON official_category(parent_id);
CREATE INDEX IF NOT EXISTS idx_official_category_active ON official_category(is_active);
CREATE INDEX IF NOT EXISTS idx_official_category_sort ON official_category(sort_order);

-- 2.3 信息表
CREATE TABLE IF NOT EXISTS information (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    source_channel_id INTEGER NOT NULL,
    official_categories TEXT, -- JSON数组存储分类ID
    reporter_id INTEGER NOT NULL,
    reporter_department_id INTEGER NOT NULL,
    priority_level VARCHAR(20) DEFAULT 'normal',
    status VARCHAR(20) DEFAULT 'pending',
    is_sensitive BOOLEAN DEFAULT FALSE,
    is_urgent BOOLEAN DEFAULT FALSE,
    deadline DATETIME,
    attachments TEXT, -- JSON数组存储附件信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_channel_id) REFERENCES source_channel(id),
    FOREIGN KEY (reporter_id) REFERENCES user(id),
    FOREIGN KEY (reporter_department_id) REFERENCES department(id)
);

CREATE INDEX IF NOT EXISTS idx_information_source ON information(source_channel_id);
CREATE INDEX IF NOT EXISTS idx_information_reporter ON information(reporter_id);
CREATE INDEX IF NOT EXISTS idx_information_department ON information(reporter_department_id);
CREATE INDEX IF NOT EXISTS idx_information_status ON information(status);
CREATE INDEX IF NOT EXISTS idx_information_priority ON information(priority_level);
CREATE INDEX IF NOT EXISTS idx_information_created_at ON information(created_at);

-- 2.4 进展记录表
CREATE TABLE IF NOT EXISTS progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    information_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    progress_type VARCHAR(20) DEFAULT 'reply',
    created_by INTEGER NOT NULL,
    department_id INTEGER NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    attachments TEXT, -- JSON数组存储附件信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (information_id) REFERENCES information(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES user(id),
    FOREIGN KEY (department_id) REFERENCES department(id)
);

CREATE INDEX IF NOT EXISTS idx_progress_information ON progress(information_id);
CREATE INDEX IF NOT EXISTS idx_progress_created_by ON progress(created_by);
CREATE INDEX IF NOT EXISTS idx_progress_department ON progress(department_id);
CREATE INDEX IF NOT EXISTS idx_progress_created_at ON progress(created_at);

-- 2.5 信息分发表
CREATE TABLE IF NOT EXISTS distribution (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    information_id INTEGER NOT NULL,
    department_id INTEGER NOT NULL,
    distributed_by INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    distributed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    withdrawn_at DATETIME,
    withdraw_reason VARCHAR(255),
    FOREIGN KEY (information_id) REFERENCES information(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES department(id),
    FOREIGN KEY (distributed_by) REFERENCES user(id),
    UNIQUE(information_id, department_id)
);

CREATE INDEX IF NOT EXISTS idx_distribution_information ON distribution(information_id);
CREATE INDEX IF NOT EXISTS idx_distribution_department ON distribution(department_id);
CREATE INDEX IF NOT EXISTS idx_distribution_status ON distribution(status);

-- ========================================
-- 3. 关联管理表
-- ========================================

-- 3.1 事件表
CREATE TABLE IF NOT EXISTS event (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE INDEX IF NOT EXISTS idx_event_type ON event(event_type);
CREATE INDEX IF NOT EXISTS idx_event_status ON event(status);
CREATE INDEX IF NOT EXISTS idx_event_created_by ON event(created_by);

-- 3.2 信息关联表
CREATE TABLE IF NOT EXISTS information_association (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    information_id_1 INTEGER NOT NULL,
    information_id_2 INTEGER NOT NULL,
    association_type VARCHAR(50) DEFAULT 'related',
    confidence_score REAL DEFAULT 0.0,
    created_by INTEGER NOT NULL,
    approved_by INTEGER,
    status VARCHAR(20) DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    approved_at DATETIME,
    FOREIGN KEY (information_id_1) REFERENCES information(id) ON DELETE CASCADE,
    FOREIGN KEY (information_id_2) REFERENCES information(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES user(id),
    FOREIGN KEY (approved_by) REFERENCES user(id),
    UNIQUE(information_id_1, information_id_2)
);

CREATE INDEX IF NOT EXISTS idx_info_assoc_info1 ON information_association(information_id_1);
CREATE INDEX IF NOT EXISTS idx_info_assoc_info2 ON information_association(information_id_2);
CREATE INDEX IF NOT EXISTS idx_info_assoc_status ON information_association(status);
CREATE INDEX IF NOT EXISTS idx_info_assoc_type ON information_association(association_type);

-- 3.3 事件关联表
CREATE TABLE IF NOT EXISTS event_association (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_id INTEGER NOT NULL,
    information_id INTEGER NOT NULL,
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES event(id) ON DELETE CASCADE,
    FOREIGN KEY (information_id) REFERENCES information(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES user(id),
    UNIQUE(event_id, information_id)
);

CREATE INDEX IF NOT EXISTS idx_event_assoc_event ON event_association(event_id);
CREATE INDEX IF NOT EXISTS idx_event_assoc_info ON event_association(information_id);

-- ========================================
-- 4. 审计日志表
-- ========================================

-- 4.1 操作日志表
CREATE TABLE IF NOT EXISTS audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    username VARCHAR(50),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id VARCHAR(50),
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_data TEXT,
    response_data TEXT,
    status VARCHAR(20) DEFAULT 'success',
    error_message TEXT,
    execution_time INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id)
);

CREATE INDEX IF NOT EXISTS idx_audit_log_user ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action);
CREATE INDEX IF NOT EXISTS idx_audit_log_resource ON audit_log(resource);
CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_log_status ON audit_log(status);
