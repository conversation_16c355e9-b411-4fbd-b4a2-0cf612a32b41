{"name": "jjwx-web", "version": "1.0.0", "description": "JJWX信息协同处理平台 - Web前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@vicons/ionicons5": "^0.12.0", "axios": "^1.6.0", "naive-ui": "^2.38.0", "pinia": "^2.1.0", "vue": "^3.4.0", "vue-router": "^4.2.0"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^5.0.0", "terser": "^5.40.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}, "engines": {"node": ">=18.0.0"}}