# 管理功能设计

## 概述
为支持分类和来源渠道的动态管理，设计了完整的管理功能模块，包括分类管理和来源渠道管理。

## 分类管理功能

### 功能需求
1. **分类列表**：显示所有上级规定分类，支持排序
2. **新增分类**：添加新的分类项
3. **编辑分类**：修改分类名称和描述
4. **启用/禁用**：控制分类的可用状态
5. **排序管理**：调整分类显示顺序
6. **删除保护**：不允许直接删除，只能禁用

### 权限控制
- **系统管理员**：完全管理权限
- **信息管理员**：完全管理权限
- **部门用户**：只读权限

### API接口设计

#### 1. 获取分类列表
```
GET /api/admin/categories
```
**响应示例**：
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "突发事件",
      "description": "各类突发性事件",
      "sort_order": 1,
      "is_active": true,
      "created_at": "2025-06-04T14:21:16+08:00",
      "updated_at": "2025-06-04T14:21:16+08:00"
    }
  ]
}
```

#### 2. 新增分类
```
POST /api/admin/categories
```
**请求体**：
```json
{
  "name": "网络安全",
  "description": "网络安全相关事件",
  "sort_order": 10
}
```

#### 3. 更新分类
```
PUT /api/admin/categories/:id
```
**请求体**：
```json
{
  "name": "网络与信息安全",
  "description": "网络与信息安全相关事件",
  "sort_order": 10
}
```

#### 4. 启用/禁用分类
```
PATCH /api/admin/categories/:id/status
```
**请求体**：
```json
{
  "is_active": false
}
```

#### 5. 批量排序
```
PUT /api/admin/categories/sort
```
**请求体**：
```json
{
  "items": [
    {"id": 1, "sort_order": 1},
    {"id": 2, "sort_order": 2}
  ]
}
```

## 来源渠道管理功能

### 功能需求
1. **渠道列表**：显示所有信息来源渠道
2. **新增渠道**：添加新的来源渠道
3. **编辑渠道**：修改渠道名称和描述
4. **启用/禁用**：控制渠道的可用状态
5. **排序管理**：调整渠道显示顺序

### API接口设计

#### 1. 获取渠道列表
```
GET /api/admin/source-channels
```

#### 2. 新增渠道
```
POST /api/admin/source-channels
```

#### 3. 更新渠道
```
PUT /api/admin/source-channels/:id
```

#### 4. 启用/禁用渠道
```
PATCH /api/admin/source-channels/:id/status
```

#### 5. 批量排序
```
PUT /api/admin/source-channels/sort
```

## 前端界面设计

### 分类管理界面
1. **列表页面**：
   - 表格显示：名称、描述、状态、排序、操作
   - 搜索功能：按名称搜索
   - 筛选功能：按状态筛选
   - 批量操作：批量启用/禁用

2. **新增/编辑弹窗**：
   - 分类名称（必填）
   - 分类描述（可选）
   - 排序值（数字）
   - 状态选择（启用/禁用）

3. **排序功能**：
   - 拖拽排序
   - 数字输入排序
   - 批量保存

### 来源渠道管理界面
- 界面设计与分类管理类似
- 功能完全对应

## 业务规则

### 分类管理规则
1. **唯一性**：分类名称不能重复
2. **删除保护**：不允许直接删除分类，只能禁用
3. **历史兼容**：禁用的分类在历史数据中仍然显示
4. **排序规则**：按sort_order升序排列，相同时按创建时间排序
5. **状态控制**：禁用的分类在新建信息时不可选择

### 来源渠道管理规则
1. **唯一性**：渠道名称不能重复
2. **删除保护**：不允许直接删除渠道，只能禁用
3. **历史兼容**：禁用的渠道在历史数据中仍然显示
4. **默认值**：新建信息时可以设置默认来源渠道

## 数据迁移考虑

### 现有数据处理
如果系统中已有使用硬编码分类的数据，需要进行数据迁移：

```sql
-- 迁移脚本示例
-- 1. 确保管理表中有对应的分类
-- 2. 将JSON中的分类名称替换为ID

UPDATE information 
SET official_categories = REPLACE(
  REPLACE(
    REPLACE(official_categories, '"突发事件"', '1'),
    '"投诉举报"', '2'
  ),
  '"安全隐患"', '7'
)
WHERE official_categories IS NOT NULL;
```

### 版本兼容性
- 新版本支持ID和名称两种格式
- 逐步迁移历史数据
- 提供数据验证工具

## 性能优化

### 缓存策略
1. **分类缓存**：将启用的分类缓存到内存
2. **渠道缓存**：将启用的渠道缓存到内存
3. **缓存更新**：管理操作后自动刷新缓存
4. **缓存时间**：设置合理的缓存过期时间

### 查询优化
1. **索引优化**：为is_active和sort_order建立索引
2. **分页查询**：管理列表支持分页
3. **条件查询**：支持按状态和名称查询

## 安全考虑

### 操作审计
1. **操作日志**：记录所有管理操作
2. **变更历史**：保留分类和渠道的变更历史
3. **操作人员**：记录操作人员信息
4. **回滚支持**：支持操作回滚

### 权限验证
1. **接口权限**：每个管理接口都需要权限验证
2. **操作权限**：不同角色有不同的操作权限
3. **数据权限**：确保只能操作有权限的数据

## 测试策略

### 单元测试
1. **API测试**：测试所有管理接口
2. **业务逻辑测试**：测试业务规则
3. **权限测试**：测试权限控制
4. **数据验证测试**：测试数据验证规则

### 集成测试
1. **前后端集成**：测试前后端交互
2. **数据库集成**：测试数据库操作
3. **缓存集成**：测试缓存机制

### E2E测试
1. **管理流程**：测试完整的管理流程
2. **用户体验**：测试用户操作体验
3. **异常处理**：测试异常情况处理

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v1.0 | 2025-06-04 14:21:16 +08:00 | AR,LD | 创建管理功能设计文档 |
