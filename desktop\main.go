// JJWX 桌面应用主入口
// {{CHENGQI:
// Action: Added
// Timestamp: 2025-06-04 15:53:00 +08:00
// Task_ID: P1-AR-001
// Principle_Applied: KISS - 简单的桌面应用，直接使用WebView打开Web页面
// Language: Go
// Description: 创建桌面应用入口，使用WebView显示Web界面，避免复杂的桌面开发
// }}

package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"runtime"
	"time"
)

func main() {
	// 启动后端服务
	go startBackendServer()

	// 等待后端服务启动
	waitForBackend()

	// 打开浏览器
	openBrowser("http://localhost:8080")

	// 保持程序运行
	select {}
}

// 启动后端服务
func startBackendServer() {
	// 获取当前目录的上级目录（项目根目录）
	if err := os.Chdir(".."); err != nil {
		log.Printf("切换到项目根目录失败: %v", err)
		return
	}

	// 启动GoFrame服务器
	cmd := exec.Command("go", "run", "main.go")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		log.Printf("启动后端服务失败: %v", err)
	}
}

// 等待后端服务启动
func waitForBackend() {
	maxRetries := 30
	for i := 0; i < maxRetries; i++ {
		resp, err := http.Get("http://localhost:8080/api/admin/v1/health")
		if err == nil && resp.StatusCode == 200 {
			resp.Body.Close()
			log.Println("后端服务已启动")
			return
		}
		if resp != nil {
			resp.Body.Close()
		}

		log.Printf("等待后端服务启动... (%d/%d)", i+1, maxRetries)
		time.Sleep(1 * time.Second)
	}

	log.Println("后端服务启动超时，但仍将打开浏览器")
}

// 打开浏览器
func openBrowser(url string) {
	var err error

	switch runtime.GOOS {
	case "linux":
		err = exec.Command("xdg-open", url).Start()
	case "windows":
		err = exec.Command("rundll32", "url.dll,FileProtocolHandler", url).Start()
	case "darwin":
		err = exec.Command("open", url).Start()
	default:
		err = fmt.Errorf("unsupported platform")
	}

	if err != nil {
		log.Printf("打开浏览器失败: %v", err)
		log.Printf("请手动打开浏览器访问: %s", url)
	} else {
		log.Printf("已打开浏览器: %s", url)
	}
}
