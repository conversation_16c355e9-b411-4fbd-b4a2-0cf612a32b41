# JJWX 多端应用项目

## 项目概述
JJWX是一款包含桌面端和Web端的综合性应用系统，采用现代化的技术栈构建。

## 技术栈
- **后端框架**: GoFrame v2.9.0
- **桌面端**: Wails框架
- **Web前端**: Vue + Naive UI
- **权限管理**: GoFrame自带RBAC（备选：Casbin集成）
- **审计系统**: 完整的操作审计和日志追踪

## 项目结构
```
jjwx/
├── backend/          # GoFrame后端服务
├── desktop/          # Wails桌面应用
├── web/             # Vue Web前端
├── shared/          # 共享代码和类型定义
├── docs/            # 项目文档
└── project_document/ # 开发过程文档
```

## 开发状态
- [x] 项目初始化
- [x] GoFrame框架集成
- [ ] 需求分析
- [ ] 架构设计
- [ ] 功能模块规划

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v1.0 | 2025-06-04 12:18:54 +08:00 | DW | 初始创建项目README |

---
*本文档遵循RIPER-5协议规范，所有修改均有完整记录*
