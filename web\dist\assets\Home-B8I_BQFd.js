import { d as defineComponent, c as createElementBlock, a as createVNode, w as withCtx, u as unref, o as openBlock, N as NSpace, b as NAlert, e as createTextVNode, f as NDescriptions, g as NDescriptionsItem, h as NCard, B as But<PERSON> } from "./index-Cx1UABdq.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Home",
  setup(__props) {
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", null, [
        createVNode(unref(NCard), { title: "欢迎使用JJWX信息协同处理平台" }, {
          default: withCtx(() => [
            createVNode(unref(NSpace), {
              vertical: "",
              size: "large"
            }, {
              default: withCtx(() => [
                createVNode(unref(NAlert), {
                  type: "success",
                  title: "系统状态"
                }, {
                  default: withCtx(() => _cache[1] || (_cache[1] = [
                    createTextVNode(" 前端应用已成功启动，正在连接后端服务... ")
                  ])),
                  _: 1,
                  __: [1]
                }),
                createVNode(unref(NDescriptions), {
                  title: "系统信息",
                  bordered: "",
                  column: 2
                }, {
                  default: withCtx(() => [
                    createVNode(unref(NDescriptionsItem), { label: "系统名称" }, {
                      default: withCtx(() => _cache[2] || (_cache[2] = [
                        createTextVNode(" JJWX信息协同处理平台 ")
                      ])),
                      _: 1,
                      __: [2]
                    }),
                    createVNode(unref(NDescriptionsItem), { label: "版本" }, {
                      default: withCtx(() => _cache[3] || (_cache[3] = [
                        createTextVNode(" v1.0.0 ")
                      ])),
                      _: 1,
                      __: [3]
                    }),
                    createVNode(unref(NDescriptionsItem), { label: "前端框架" }, {
                      default: withCtx(() => _cache[4] || (_cache[4] = [
                        createTextVNode(" Vue 3 + Naive UI ")
                      ])),
                      _: 1,
                      __: [4]
                    }),
                    createVNode(unref(NDescriptionsItem), { label: "后端框架" }, {
                      default: withCtx(() => _cache[5] || (_cache[5] = [
                        createTextVNode(" GoFrame v2.9.0 ")
                      ])),
                      _: 1,
                      __: [5]
                    })
                  ]),
                  _: 1
                }),
                createVNode(unref(NCard), { title: "快速开始" }, {
                  default: withCtx(() => [
                    createVNode(unref(NSpace), null, {
                      default: withCtx(() => [
                        createVNode(unref(Button), {
                          type: "primary",
                          onClick: _cache[0] || (_cache[0] = ($event) => _ctx.$router.push("/test"))
                        }, {
                          default: withCtx(() => _cache[6] || (_cache[6] = [
                            createTextVNode(" 测试后端连接 ")
                          ])),
                          _: 1,
                          __: [6]
                        }),
                        createVNode(unref(Button), null, {
                          default: withCtx(() => _cache[7] || (_cache[7] = [
                            createTextVNode("信息管理")
                          ])),
                          _: 1,
                          __: [7]
                        }),
                        createVNode(unref(Button), null, {
                          default: withCtx(() => _cache[8] || (_cache[8] = [
                            createTextVNode("关联管理")
                          ])),
                          _: 1,
                          __: [8]
                        }),
                        createVNode(unref(Button), null, {
                          default: withCtx(() => _cache[9] || (_cache[9] = [
                            createTextVNode("系统管理")
                          ])),
                          _: 1,
                          __: [9]
                        })
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                })
              ]),
              _: 1
            })
          ]),
          _: 1
        })
      ]);
    };
  }
});
export {
  _sfc_main as default
};
