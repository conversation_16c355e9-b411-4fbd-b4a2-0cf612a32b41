# 信息关联机制设计（简化版）

## 概述
信息关联机制是JJWX平台的核心功能，支持同一事件的多条信息关联，实现进展共享和协同处理。采用简化设计，优先实现手动关联功能。

## 关联类型设计

### 1. 直接关联（Information-to-Information）
**定义**：两条信息直接关联，表示它们属于同一事件或高度相关。

**关联类型枚举**：
- `SAME_EVENT`：同一事件的不同信息
- `RELATED_EVENT`：相关事件信息
- `FOLLOW_UP`：后续跟进信息
- `DUPLICATE`：重复信息（需要合并处理）
- `REFERENCE`：引用关系

### 2. 事件关联（Information-to-Event）
**定义**：多条信息关联到同一个事件实体，通过事件统一管理。

**事件类型**：
- 突发事件
- 常规事件
- 专项事件
- 综合事件

## 手动关联功能设计

### 关联创建方式
1. **搜索关联**：用户通过搜索功能查找相关信息进行关联
2. **浏览关联**：用户浏览信息列表时手动选择关联
3. **批量关联**：支持一次性关联多条信息到同一事件

### 关联搜索功能
**搜索条件**：
- 按内容关键词搜索
- 按时间范围搜索
- 按部门搜索
- 按分类搜索
- 按地点搜索

**搜索结果展示**：
- 显示信息摘要
- 显示创建时间
- 显示报送部门
- 显示当前状态
- 提供关联操作按钮

## 数据库设计

### 核心表结构

#### 1. information表（信息主表）
```sql
CREATE TABLE information (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50),
    department_id INTEGER NOT NULL,
    reporter_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    priority INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES department(id),
    FOREIGN KEY (reporter_id) REFERENCES user(id)
);
```

#### 2. event表（事件表）
```sql
CREATE TABLE event (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES user(id)
);
```

#### 3. information_association表（信息关联表）
```sql
CREATE TABLE information_association (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_info_id INTEGER NOT NULL,
    target_info_id INTEGER NOT NULL,
    association_type VARCHAR(20) NOT NULL,
    confidence_score DECIMAL(3,2),
    created_by INTEGER NOT NULL,
    approved_by INTEGER,
    status VARCHAR(20) DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    approved_at DATETIME,
    FOREIGN KEY (source_info_id) REFERENCES information(id),
    FOREIGN KEY (target_info_id) REFERENCES information(id),
    FOREIGN KEY (created_by) REFERENCES user(id),
    FOREIGN KEY (approved_by) REFERENCES user(id),
    UNIQUE(source_info_id, target_info_id)
);
```

#### 4. event_association表（事件关联表）
```sql
CREATE TABLE event_association (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    information_id INTEGER NOT NULL,
    event_id INTEGER NOT NULL,
    association_type VARCHAR(20) DEFAULT 'belongs_to',
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (information_id) REFERENCES information(id),
    FOREIGN KEY (event_id) REFERENCES event(id),
    FOREIGN KEY (created_by) REFERENCES user(id),
    UNIQUE(information_id, event_id)
);
```



## 业务流程设计

### 1. 手动关联流程
```
用户搜索 → 选择关联信息 → 选择关联类型 → 提交申请 → 审批（可选） → 关联生效
```

### 2. 关联审批流程
```
关联申请 → 管理部门审核 → 审批通过/拒绝 → 通知申请人 → 关联生效/失效
```

### 3. 关联解除流程
```
用户申请解除 → 填写解除原因 → 管理部门审核 → 关联解除 → 记录操作日志
```

## 权限控制设计

### 关联操作权限
- **管理部门**：可创建、修改、删除任何关联
- **报送部门**：可申请关联自己报送的信息
- **处理部门**：可申请关联正在处理的信息
- **查看权限**：有任一关联信息查看权限即可查看关联关系

### 进展共享权限
- 用户只能查看自己有权限的信息的进展
- 关联不会扩大用户的信息查看权限
- 进展展示时需要进行权限过滤

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v2.0 | 2025-06-04 15:30:04 +08:00 | AR,LD | 简化设计：去掉智能关联算法，改为手动关联，删除关联建议表 |
| v1.0 | 2025-06-04 13:14:46 +08:00 | AR,LD | 创建信息关联机制详细设计文档 |
