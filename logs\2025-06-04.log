2025-06-04T16:00:09.952+08:00 [INFO] openapi specification is disabled
2025-06-04T16:00:09.952+08:00 [INFO] pid[3044]: http server started listening on [:8080]

  ADDRESS | METHOD |           ROUTE           |      HANDLER       |    MIDDLEWARE      
----------|--------|---------------------------|--------------------|--------------------
  :8080   | ALL    | /*                        | main.middlewareLog | GLOBAL MIDDLEWARE  
----------|--------|---------------------------|--------------------|--------------------
  :8080   | GET    | /api/admin/v1/health      | main.main.func1    |                    
----------|--------|---------------------------|--------------------|--------------------
  :8080   | GET    | /api/admin/v1/system/info | main.main.func2    |                    
----------|--------|---------------------------|--------------------|--------------------

2025-06-04T16:22:06.853+08:00 [FATA] net.Listen address ":8080" failed: listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
1. net.Listen address ":8080" failed
2. listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.

