# JJWX 数据结构设计 v1.0

## 概述
完整的数据库表结构设计，包含用户权限、信息管理、关联管理、审计日志等所有核心功能模块的数据表。

## 数据库设计原则

### 1. 设计原则
- **规范化**：遵循第三范式，避免数据冗余
- **性能优化**：合理的索引设计和查询优化
- **扩展性**：支持未来功能扩展
- **一致性**：外键约束保证数据一致性
- **安全性**：敏感数据加密存储

### 2. 命名规范
- **表名**：小写字母，下划线分隔，复数形式
- **字段名**：小写字母，下划线分隔
- **索引名**：idx_表名_字段名
- **外键名**：fk_表名_字段名

## 核心表结构设计

### 1. 用户权限管理表

#### 1.1 用户表 (user)
```sql
CREATE TABLE user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    department_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES department(id)
);

CREATE INDEX idx_user_username ON user(username);
CREATE INDEX idx_user_department ON user(department_id);
CREATE INDEX idx_user_status ON user(status);
```

#### 1.2 部门表 (department)
```sql
CREATE TABLE department (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    parent_id INTEGER,
    level INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES department(id)
);

CREATE INDEX idx_department_parent ON department(parent_id);
CREATE INDEX idx_department_active ON department(is_active);
CREATE INDEX idx_department_sort ON department(sort_order);
```

#### 1.3 角色表 (role)
```sql
CREATE TABLE role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_role_active ON role(is_active);
```

#### 1.4 权限表 (permission)
```sql
CREATE TABLE permission (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_permission_resource ON permission(resource);
CREATE INDEX idx_permission_action ON permission(action);
CREATE INDEX idx_permission_active ON permission(is_active);
```

#### 1.5 用户角色关联表 (user_role)
```sql
CREATE TABLE user_role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
    UNIQUE(user_id, role_id)
);

CREATE INDEX idx_user_role_user ON user_role(user_id);
CREATE INDEX idx_user_role_role ON user_role(role_id);
```

#### 1.6 角色权限关联表 (role_permission)
```sql
CREATE TABLE role_permission (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    role_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permission(id) ON DELETE CASCADE,
    UNIQUE(role_id, permission_id)
);

CREATE INDEX idx_role_permission_role ON role_permission(role_id);
CREATE INDEX idx_role_permission_permission ON role_permission(permission_id);
```

### 2. 信息管理表

#### 2.1 信息表 (information) - 已在之前文档中定义
```sql
-- 参考 information_entity_design.md 中的完整定义
```

#### 2.2 进展记录表 (progress)
```sql
CREATE TABLE progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    information_id INTEGER NOT NULL,
    content TEXT NOT NULL,
    progress_type VARCHAR(20) DEFAULT 'reply',
    created_by INTEGER NOT NULL,
    department_id INTEGER NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    attachments TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (information_id) REFERENCES information(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES user(id),
    FOREIGN KEY (department_id) REFERENCES department(id)
);

CREATE INDEX idx_progress_information ON progress(information_id);
CREATE INDEX idx_progress_created_by ON progress(created_by);
CREATE INDEX idx_progress_department ON progress(department_id);
CREATE INDEX idx_progress_created_at ON progress(created_at);
```

#### 2.3 信息分发表 (distribution)
```sql
CREATE TABLE distribution (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    information_id INTEGER NOT NULL,
    department_id INTEGER NOT NULL,
    distributed_by INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    distributed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    withdrawn_at DATETIME,
    withdraw_reason VARCHAR(255),
    FOREIGN KEY (information_id) REFERENCES information(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES department(id),
    FOREIGN KEY (distributed_by) REFERENCES user(id),
    UNIQUE(information_id, department_id)
);

CREATE INDEX idx_distribution_information ON distribution(information_id);
CREATE INDEX idx_distribution_department ON distribution(department_id);
CREATE INDEX idx_distribution_status ON distribution(status);
```

### 3. 关联管理表

#### 3.1 事件表 (event)
```sql
CREATE TABLE event (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES user(id)
);

CREATE INDEX idx_event_type ON event(event_type);
CREATE INDEX idx_event_status ON event(status);
CREATE INDEX idx_event_created_by ON event(created_by);
```

#### 3.2 信息关联表 (information_association) - 已在之前文档中定义
```sql
-- 参考 information_association_design.md 中的完整定义
```

#### 3.3 事件关联表 (event_association) - 已在之前文档中定义
```sql
-- 参考 information_association_design.md 中的完整定义
```

#### 3.4 关联建议表 (association_suggestion) - 已在之前文档中定义
```sql
-- 参考 information_association_design.md 中的完整定义
```

### 4. 管理功能表

#### 4.1 上级规定分类表 (official_category) - 已在之前文档中定义
```sql
-- 参考 information_entity_design.md 中的完整定义
```

#### 4.2 信息来源渠道表 (source_channel) - 已在之前文档中定义
```sql
-- 参考 information_entity_design.md 中的完整定义
```

### 5. 审计日志表

#### 5.1 操作日志表 (audit_log)
```sql
CREATE TABLE audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    username VARCHAR(50),
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id VARCHAR(50),
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_data TEXT,
    response_data TEXT,
    status VARCHAR(20) DEFAULT 'success',
    error_message TEXT,
    execution_time INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id)
);

CREATE INDEX idx_audit_log_user ON audit_log(user_id);
CREATE INDEX idx_audit_log_action ON audit_log(action);
CREATE INDEX idx_audit_log_resource ON audit_log(resource);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX idx_audit_log_status ON audit_log(status);
```

#### 5.2 数据变更日志表 (data_change_log)
```sql
CREATE TABLE data_change_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(50) NOT NULL,
    operation VARCHAR(20) NOT NULL,
    old_data TEXT,
    new_data TEXT,
    changed_fields TEXT,
    user_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id)
);

CREATE INDEX idx_data_change_log_table ON data_change_log(table_name);
CREATE INDEX idx_data_change_log_record ON data_change_log(record_id);
CREATE INDEX idx_data_change_log_operation ON data_change_log(operation);
CREATE INDEX idx_data_change_log_user ON data_change_log(user_id);
CREATE INDEX idx_data_change_log_created_at ON data_change_log(created_at);
```

### 6. 系统配置表

#### 6.1 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string',
    description VARCHAR(255),
    is_public BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_system_config_key ON system_config(config_key);
CREATE INDEX idx_system_config_public ON system_config(is_public);
```

#### 6.2 通知模板表 (notification_template)
```sql
CREATE TABLE notification_template (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    template_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_notification_template_type ON notification_template(template_type);
CREATE INDEX idx_notification_template_active ON notification_template(is_active);
```

### 7. 附件管理表

#### 7.1 附件表 (attachment)
```sql
CREATE TABLE attachment (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    file_type VARCHAR(100),
    mime_type VARCHAR(100),
    related_table VARCHAR(100),
    related_id INTEGER,
    uploaded_by INTEGER NOT NULL,
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES user(id)
);

CREATE INDEX idx_attachment_related ON attachment(related_table, related_id);
CREATE INDEX idx_attachment_uploaded_by ON attachment(uploaded_by);
CREATE INDEX idx_attachment_created_at ON attachment(created_at);
```

## 表关系图

### 核心关系
```
user ←→ department (多对一)
user ←→ role (多对多，通过user_role)
role ←→ permission (多对多，通过role_permission)
information ←→ user (多对一，reporter)
information ←→ department (多对一，reporter_department)
information ←→ source_channel (多对一)
information ←→ official_category (多对多，通过JSON数组)
information ←→ progress (一对多)
information ←→ distribution (一对多)
information ←→ information_association (多对多)
information ←→ event (多对多，通过event_association)
```

### 审计关系
```
所有业务表 → audit_log (操作审计)
所有业务表 → data_change_log (数据变更审计)
```

## 数据初始化

### 1. 默认角色数据
```sql
INSERT INTO role (name, description, is_system) VALUES
('系统管理员', '系统管理员角色', TRUE),
('信息管理员', '信息管理员角色', TRUE),
('部门用户', '普通部门用户角色', TRUE);
```

### 2. 默认权限数据
```sql
INSERT INTO permission (name, description, resource, action) VALUES
('信息查看', '查看信息权限', 'information', 'read'),
('信息创建', '创建信息权限', 'information', 'create'),
('信息编辑', '编辑信息权限', 'information', 'update'),
('信息删除', '删除信息权限', 'information', 'delete'),
('信息审核', '审核信息权限', 'information', 'approve'),
('信息分发', '分发信息权限', 'information', 'distribute'),
('分类管理', '管理分类权限', 'category', 'manage'),
('用户管理', '管理用户权限', 'user', 'manage');
```

### 3. 默认部门数据
```sql
INSERT INTO department (name, description, level) VALUES
('信息管理部', '负责信息管理的部门', 1),
('技术部', '技术支持部门', 1),
('业务部', '业务处理部门', 1);
```

## 性能优化策略

### 1. 索引策略
- **主键索引**：所有表都有自增主键
- **外键索引**：所有外键字段都建立索引
- **查询索引**：常用查询字段建立索引
- **复合索引**：多字段查询建立复合索引

### 2. 查询优化
- **分页查询**：使用LIMIT和OFFSET
- **条件查询**：合理使用WHERE条件
- **JOIN优化**：避免不必要的JOIN操作
- **子查询优化**：使用EXISTS替代IN

### 3. 数据归档
- **历史数据归档**：定期归档历史数据
- **日志清理**：定期清理过期日志
- **索引维护**：定期重建索引

## 数据安全

### 1. 敏感数据加密
- **密码加密**：使用bcrypt加密
- **敏感字段加密**：使用AES加密
- **数据库文件加密**：SQLite文件加密

### 2. 数据备份
- **定时备份**：每日自动备份
- **增量备份**：支持增量备份
- **备份验证**：定期验证备份完整性

### 3. 访问控制
- **连接限制**：限制数据库连接数
- **权限控制**：最小权限原则
- **审计日志**：完整的操作审计

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v1.0 | 2025-06-04 14:47:24 +08:00 | AR,LD | 创建完整数据结构设计文档 |
