-- JJWX 信息协同处理平台 - 初始数据脚本
-- {{CHENGQI:
-- Action: Added
-- Timestamp: 2025-06-04 16:25:00 +08:00
-- Task_ID: P1-LD-002
-- Principle_Applied: KISS - 简洁的初始数据，只包含必要的基础数据
-- Language: SQL
-- Description: 插入系统运行必需的基础数据，包含默认角色、权限、部门等
-- }}

-- ========================================
-- 1. 默认部门数据
-- ========================================
INSERT OR IGNORE INTO department (id, name, description, level) VALUES
(1, '信息管理部', '负责信息管理的部门', 1),
(2, '技术部', '技术支持部门', 1),
(3, '业务部', '业务处理部门', 1),
(4, '综合部', '综合管理部门', 1);

-- ========================================
-- 2. 默认角色数据
-- ========================================
INSERT OR IGNORE INTO role (id, name, description, is_system) VALUES
(1, '系统管理员', '系统管理员角色，拥有所有权限', TRUE),
(2, '信息管理员', '信息管理员角色，负责信息审核和管理', TRUE),
(3, '部门用户', '普通部门用户角色，可以查看和处理分配的信息', TRUE),
(4, '只读用户', '只读用户角色，只能查看信息', TRUE);

-- ========================================
-- 3. 默认权限数据
-- ========================================
INSERT OR IGNORE INTO permission (id, name, description, resource, action) VALUES
-- 信息管理权限
(1, '信息查看', '查看信息权限', 'information', 'read'),
(2, '信息创建', '创建信息权限', 'information', 'create'),
(3, '信息编辑', '编辑信息权限', 'information', 'update'),
(4, '信息删除', '删除信息权限', 'information', 'delete'),
(5, '信息审核', '审核信息权限', 'information', 'approve'),
(6, '信息分发', '分发信息权限', 'information', 'distribute'),
(7, '信息撤回', '撤回信息权限', 'information', 'withdraw'),

-- 关联管理权限
(8, '关联查看', '查看关联权限', 'association', 'read'),
(9, '关联创建', '创建关联权限', 'association', 'create'),
(10, '关联删除', '删除关联权限', 'association', 'delete'),
(11, '关联审核', '审核关联权限', 'association', 'approve'),

-- 用户管理权限
(12, '用户查看', '查看用户权限', 'user', 'read'),
(13, '用户创建', '创建用户权限', 'user', 'create'),
(14, '用户编辑', '编辑用户权限', 'user', 'update'),
(15, '用户删除', '删除用户权限', 'user', 'delete'),

-- 系统管理权限
(16, '分类管理', '管理分类权限', 'category', 'manage'),
(17, '渠道管理', '管理渠道权限', 'channel', 'manage'),
(18, '系统配置', '系统配置权限', 'system', 'config'),
(19, '审计查看', '查看审计日志权限', 'audit', 'read');

-- ========================================
-- 4. 角色权限关联
-- ========================================
-- 系统管理员：所有权限
INSERT OR IGNORE INTO role_permission (role_id, permission_id) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7),
(1, 8), (1, 9), (1, 10), (1, 11),
(1, 12), (1, 13), (1, 14), (1, 15),
(1, 16), (1, 17), (1, 18), (1, 19);

-- 信息管理员：信息和关联管理权限
INSERT OR IGNORE INTO role_permission (role_id, permission_id) VALUES
(2, 1), (2, 2), (2, 3), (2, 5), (2, 6), (2, 7),
(2, 8), (2, 9), (2, 11),
(2, 12), (2, 16), (2, 17);

-- 部门用户：基础信息操作权限
INSERT OR IGNORE INTO role_permission (role_id, permission_id) VALUES
(3, 1), (3, 2), (3, 3),
(3, 8), (3, 9);

-- 只读用户：仅查看权限
INSERT OR IGNORE INTO role_permission (role_id, permission_id) VALUES
(4, 1), (4, 8);

-- ========================================
-- 5. 默认管理员用户
-- ========================================
-- 密码: admin123 (实际应用中需要使用bcrypt加密)
INSERT OR IGNORE INTO user (id, username, password, real_name, email, department_id, status) VALUES
(1, 'admin', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcQjyN/L6', '系统管理员', '<EMAIL>', 1, 'active');

-- 为管理员分配系统管理员角色
INSERT OR IGNORE INTO user_role (user_id, role_id) VALUES (1, 1);

-- ========================================
-- 6. 默认信息来源渠道
-- ========================================
INSERT OR IGNORE INTO source_channel (id, name, description) VALUES
(1, '网络举报', '通过网络平台举报的信息'),
(2, '电话举报', '通过电话举报的信息'),
(3, '现场举报', '现场举报的信息'),
(4, '邮件举报', '通过邮件举报的信息'),
(5, '其他渠道', '其他渠道来源的信息');

-- ========================================
-- 7. 默认上级规定分类
-- ========================================
INSERT OR IGNORE INTO official_category (id, name, description, level) VALUES
(1, '安全类', '涉及安全相关的信息', 1),
(2, '质量类', '涉及质量相关的信息', 1),
(3, '环境类', '涉及环境相关的信息', 1),
(4, '管理类', '涉及管理相关的信息', 1),
(5, '其他类', '其他类型的信息', 1);
