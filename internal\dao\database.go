// JJWX 数据库初始化和管理
// {{CHENGQI:
// Action: Added
// Timestamp: 2025-06-04 16:25:00 +08:00
// Task_ID: P1-LD-002
// Principle_Applied: KISS - 简洁的数据库初始化逻辑，只包含必要的功能
// Language: Go
// Description: 实现数据库连接、初始化和基础操作，使用GoFrame ORM
// }}

package dao

import (
	"context"
	"errors"
	"path/filepath"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"

	_ "github.com/mattn/go-sqlite3"
)

// Database 数据库管理结构
type Database struct {
	db gdb.DB
}

// NewDatabase 创建数据库管理实例
func NewDatabase() *Database {
	return &Database{
		db: g.DB(),
	}
}

// Initialize 初始化数据库
func (d *Database) Initialize(ctx context.Context) error {
	// 确保数据目录存在
	dataDir := "data"
	if !gfile.Exists(dataDir) {
		if err := gfile.Mkdir(dataDir); err != nil {
			return err
		}
	}

	// 检查数据库是否需要初始化
	dbPath := filepath.Join(dataDir, "jjwx.db")
	needInit := !gfile.Exists(dbPath)

	// 测试数据库连接
	if err := d.db.PingMaster(); err != nil {
		glog.Error(ctx, "数据库连接失败:", err)
		return err
	}

	glog.Info(ctx, "数据库连接成功")

	// 如果需要初始化，执行初始化脚本
	if needInit {
		glog.Info(ctx, "检测到新数据库，开始初始化...")
		if err := d.initializeSchema(ctx); err != nil {
			return err
		}
		if err := d.initializeData(ctx); err != nil {
			return err
		}
		glog.Info(ctx, "数据库初始化完成")
	} else {
		glog.Info(ctx, "数据库已存在，跳过初始化")
	}

	return nil
}

// initializeSchema 初始化数据库表结构
func (d *Database) initializeSchema(ctx context.Context) error {
	schemaFile := "manifest/config/database_init.sql"
	if !gfile.Exists(schemaFile) {
		return errors.New("数据库初始化脚本不存在: " + schemaFile)
	}

	sqlContent := gfile.GetContents(schemaFile)
	if sqlContent == "" {
		return errors.New("数据库初始化脚本为空")
	}

	glog.Info(ctx, "执行数据库表结构初始化...")

	// 执行SQL脚本
	if _, err := d.db.Exec(ctx, sqlContent); err != nil {
		glog.Error(ctx, "执行数据库初始化脚本失败:", err)
		return err
	}

	glog.Info(ctx, "数据库表结构初始化完成")
	return nil
}

// initializeData 初始化基础数据
func (d *Database) initializeData(ctx context.Context) error {
	dataFile := "manifest/config/database_data.sql"
	if !gfile.Exists(dataFile) {
		return errors.New("数据库数据初始化脚本不存在: " + dataFile)
	}

	sqlContent := gfile.GetContents(dataFile)
	if sqlContent == "" {
		return errors.New("数据库数据初始化脚本为空")
	}

	glog.Info(ctx, "执行基础数据初始化...")

	// 执行SQL脚本
	if _, err := d.db.Exec(ctx, sqlContent); err != nil {
		glog.Error(ctx, "执行数据初始化脚本失败:", err)
		return err
	}

	glog.Info(ctx, "基础数据初始化完成")
	return nil
}

// GetDB 获取数据库实例
func (d *Database) GetDB() gdb.DB {
	return d.db
}

// CheckHealth 检查数据库健康状态
func (d *Database) CheckHealth(ctx context.Context) error {
	return d.db.PingMaster()
}

// GetStats 获取数据库统计信息
func (d *Database) GetStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取表数量
	tables, err := d.db.Tables(ctx)
	if err != nil {
		return nil, err
	}
	stats["table_count"] = len(tables)
	stats["tables"] = tables

	// 获取用户数量
	userCount, err := d.db.Model("user").Count()
	if err != nil {
		glog.Warning(ctx, "获取用户数量失败:", err)
		stats["user_count"] = 0
	} else {
		stats["user_count"] = userCount
	}

	// 获取信息数量
	infoCount, err := d.db.Model("information").Count()
	if err != nil {
		glog.Warning(ctx, "获取信息数量失败:", err)
		stats["information_count"] = 0
	} else {
		stats["information_count"] = infoCount
	}

	return stats, nil
}

// Close 关闭数据库连接
func (d *Database) Close(ctx context.Context) error {
	if d.db != nil {
		return d.db.Close(ctx)
	}
	return nil
}

// 全局数据库实例
var globalDB *Database

// InitGlobalDB 初始化全局数据库实例
func InitGlobalDB(ctx context.Context) error {
	globalDB = NewDatabase()
	return globalDB.Initialize(ctx)
}

// GetGlobalDB 获取全局数据库实例
func GetGlobalDB() *Database {
	return globalDB
}

// CloseGlobalDB 关闭全局数据库连接
func CloseGlobalDB(ctx context.Context) error {
	if globalDB != nil {
		return globalDB.Close(ctx)
	}
	return nil
}
