// JJWX 信息协同处理平台 - 主程序入口
// {{CHENGQI:
// Action: Added
// Timestamp: 2025-06-04 15:53:00 +08:00
// Task_ID: P1-AR-001
// Principle_Applied: KISS - 简单的服务器启动代码，只包含必要的初始化逻辑
// Language: Go
// Description: 创建GoFrame服务器主入口，包含基本的路由和中间件配置
// }}

package main

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"

	"jjwx/internal/dao"
)

func main() {
	// 初始化数据库
	ctx := context.Background()
	if err := dao.InitGlobalDB(ctx); err != nil {
		glog.Fatal(ctx, "数据库初始化失败:", err)
	}
	defer dao.CloseGlobalDB(ctx)

	// 创建HTTP服务器
	s := g.Server()

	// 配置静态文件服务
	s.SetServerRoot("resource/public")
	s.AddStaticPath("/", "web/dist")

	// 配置中间件
	s.Use(middlewareCORS)
	s.Use(middlewareLog)

	// 配置API路由组
	apiGroup := s.Group("/api/admin/v1")
	{
		// 健康检查接口
		apiGroup.GET("/health", func(r *ghttp.Request) {
			r.Response.WriteJson(g.Map{
				"code":    0,
				"message": "success",
				"data": g.Map{
					"status":  "healthy",
					"service": "JJWX信息协同处理平台",
					"version": "1.0.0",
				},
			})
		})

		// 系统信息接口
		apiGroup.GET("/system/info", func(r *ghttp.Request) {
			r.Response.WriteJson(g.Map{
				"code":    0,
				"message": "success",
				"data": g.Map{
					"name":        "JJWX信息协同处理平台",
					"version":     "1.0.0",
					"description": "信息协同处理平台",
					"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
				},
			})
		})

		// 数据库状态检查接口
		apiGroup.GET("/database/status", func(r *ghttp.Request) {
			db := dao.GetGlobalDB()
			if db == nil {
				r.Response.WriteJson(g.Map{
					"code":    1001,
					"message": "数据库未初始化",
					"data":    nil,
				})
				return
			}

			// 检查数据库健康状态
			if err := db.CheckHealth(r.Context()); err != nil {
				r.Response.WriteJson(g.Map{
					"code":    1002,
					"message": "数据库连接失败",
					"data":    g.Map{"error": err.Error()},
				})
				return
			}

			// 获取数据库统计信息
			stats, err := db.GetStats(r.Context())
			if err != nil {
				r.Response.WriteJson(g.Map{
					"code":    1003,
					"message": "获取数据库统计信息失败",
					"data":    g.Map{"error": err.Error()},
				})
				return
			}

			r.Response.WriteJson(g.Map{
				"code":    0,
				"message": "success",
				"data": g.Map{
					"status": "healthy",
					"stats":  stats,
				},
			})
		})
	}

	// 确保数据目录存在
	if !gfile.Exists("data") {
		if err := gfile.Mkdir("data"); err != nil {
			glog.Fatal(context.Background(), "创建数据目录失败:", err)
		}
	}

	// 确保日志目录存在
	if !gfile.Exists("logs") {
		if err := gfile.Mkdir("logs"); err != nil {
			glog.Fatal(context.Background(), "创建日志目录失败:", err)
		}
	}

	// 启动服务器
	glog.Info(context.Background(), "JJWX信息协同处理平台启动中...")
	glog.Info(context.Background(), fmt.Sprintf("服务器地址: http://localhost%s", g.Cfg().MustGet(context.Background(), "server.address").String()))

	s.Run()
}

// CORS中间件
func middlewareCORS(r *ghttp.Request) {
	r.Response.CORSDefault()
	r.Middleware.Next()
}

// 日志中间件
func middlewareLog(r *ghttp.Request) {
	r.Middleware.Next()

	// 记录访问日志
	glog.Info(r.Context(), fmt.Sprintf(
		"[%s] %s %s - %d",
		r.Method,
		r.RequestURI,
		r.GetClientIp(),
		r.Response.Status,
	))
}
